from django.db import models
from drivers.models import Driver

class Location(models.Model):
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE)
    latitude = models.DecimalField(max_digits=9, decimal_places=6)
    longitude = models.DecimalField(max_digits=9, decimal_places=6)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.driver.username} @ {self.timestamp}"

    class Meta:
        verbose_name = "موقع"
        verbose_name_plural = "المواقع"
        ordering = ['-timestamp']
