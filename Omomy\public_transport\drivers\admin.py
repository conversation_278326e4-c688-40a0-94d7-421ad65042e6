from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from .models import Driver, Village

@admin.register(Village)
class VillageAdmin(admin.ModelAdmin):
    list_display = ('name', 'governorate', 'driver_count')
    search_fields = ('name', 'governorate')
    list_filter = ('governorate',)
    list_per_page = 20

    def driver_count(self, obj):
        try:
            count = obj.driver_set.count()
            return format_html('<span style="font-weight: bold;">{}</span>', count)
        except Exception as e:
            return format_html('<span style="color: red;">خطأ</span>')
    driver_count.short_description = 'عدد السائقين'

@admin.register(Driver)
class DriverAdmin(UserAdmin):
    list_display = ('username', 'village', 'phone_number', 'duty_status', 'location_info', 'last_update')
    list_filter = ('village__name', 'is_on_duty', 'is_active')
    search_fields = ('username', 'village', 'phone_number', 'first_name', 'last_name')
    list_per_page = 25
    ordering = ('username',)

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('معلومات شخصية', {
            'fields': ('first_name', 'last_name', 'email', 'phone_number'),
            'classes': ('wide',)
        }),
        ('معلومات القرية والدوام', {
            'fields': ('village', 'is_on_duty', 'permitted_routes'),
            'classes': ('wide',)
        }),
        ('معلومات الموقع', {
            'fields': ('current_latitude', 'current_longitude', 'last_location_update'),
            'classes': ('wide',)
        }),
        ('الأذونات', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
            'classes': ('collapse',)
        }),
        ('تواريخ مهمة', {
            'fields': ('last_login', 'date_joined'),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = UserAdmin.add_fieldsets + (
        ('معلومات السائق', {
            'fields': ('phone_number', 'village', 'is_on_duty', 'permitted_routes'),
            'classes': ('wide',)
        }),
    )

    def duty_status(self, obj):
        if obj.is_on_duty:
            return format_html('<span style="color: green; font-weight: bold;">✓ نشط</span>')
        return format_html('<span style="color: red; font-weight: bold;">✗ غير نشط</span>')
    duty_status.short_description = 'حالة الدوام'

    def location_info(self, obj):
        if obj.current_latitude is not None and obj.current_longitude is not None:
            try:
                # Handle both Decimal and float types
                lat = float(str(obj.current_latitude))
                lng = float(str(obj.current_longitude))
                return format_html(
                    '<span style="font-family: monospace; background: #f0f0f0; padding: 2px 6px; border-radius: 3px;">{:.6f}, {:.6f}</span>',
                    lat, lng
                )
            except (ValueError, TypeError, AttributeError) as e:
                return format_html('<span style="color: red;">خطأ في الإحداثيات: {}</span>', str(e)[:20])
        return format_html('<span style="color: gray;">لا يوجد موقع</span>')
    location_info.short_description = 'الموقع الحالي'

    def last_update(self, obj):
        if obj.last_location_update:
            try:
                formatted_time = obj.last_location_update.strftime('%Y-%m-%d %H:%M:%S')
                return format_html(
                    '<span style="font-size: 12px;">{}</span>',
                    formatted_time
                )
            except (AttributeError, ValueError):
                return format_html('<span style="color: red;">خطأ في التاريخ</span>')
        return format_html('<span style="color: gray;">لم يتم التحديث</span>')
    last_update.short_description = 'آخر تحديث'

    class Meta:
        verbose_name = "سائق"
        verbose_name_plural = "السائقين"
