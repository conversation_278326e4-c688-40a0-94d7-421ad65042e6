from django.db import migrations, models

def populate_villages(apps, schema_editor):
    Village = apps.get_model('drivers', 'Village')
    Driver = apps.get_model('drivers', 'Driver')
    
    # Grouped by governorate for better organization
    villages_by_governorate = {
        'Jenin': ['يعبد', 'ميثلون', 'الزبابدة', 'سيريس', 'كفر دان'],
        'Tubas': ['طيوبه', 'طمون', 'أوصرين', 'الرأس الأحمر', 'الفرعة'],
        'Tulkarm': ['علار', 'ذنابة', 'شويكة', 'كفر راعي', 'باقة الحطب'],
        'Nablus': ['سبسطية', 'بورين', 'حوارة', 'قريوت', 'ياصيد'],
        'Qalqilya': ['عزون', 'كفر ثلث', 'فرعون', 'يانوح', 'إماتين'],
        'Salfit': ['بُرقة', 'دير استيا', 'كفل حارس', 'إسكاكا', 'ياسوف'],
        'Rama<PERSON>': ['دير دبوان', 'بيرزيت', 'سلواد', 'عبوين', 'المزرعة القبلية'],
        'Jericho': ['العوجا', 'النويعمة', 'فصايل', 'الديوك', 'مرج نعجة'],
        'Jerusalem': ['عناتا', 'أبو ديس', 'الرام', 'الزعيم', 'حزما'],
        'Bethlehem': ['الخضر', 'نحالين', 'بتير', 'حوسان', 'الولجة'],
        'Hebron': ['الظاهرية', 'السموع', 'دورا', 'يطا', 'الفقير']
    }

    villages = []
    for gov, village_list in villages_by_governorate.items():
        villages.extend([{'name': v, 'governorate': gov} for v in village_list])

    for village in villages:
        Village.objects.create(name=village['name'], governorate=village['governorate'])
    
    # Assign default village to any existing drivers
    default_village = Village.objects.first()
    Driver.objects.update(village=default_village)

class Migration(migrations.Migration):
    dependencies = [
        ('drivers', '0002_add_governorate'),
    ]

    operations = [
        migrations.RunPython(populate_villages),
    ]
