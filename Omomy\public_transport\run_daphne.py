#!/usr/bin/env python3
"""
PythonAnywhere Daphne Runner Script
===================================

This script is designed to run your Django Channels project on PythonAnywhere
using Daphne ASGI server instead of the default runserver.

Usage:
    python run_daphne.py [port]

Default port is 8000 if not specified.

For PythonAnywhere console:
    python run_daphne.py 8000

This script handles:
- Environment setup for PythonAnywhere
- Django settings configuration
- Static files collection
- Database migrations
- Daphne server startup with proper ASGI application
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('daphne_startup.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Setup environment variables for Django"""
    logger.info("Setting up environment variables...")
    
    # Set Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'public_transport.settings')
    
    # Set production-like environment variables
    os.environ.setdefault('DJANGO_ALLOW_ASYNC_UNSAFE', 'true')
    
    # Add current directory to Python path
    current_dir = Path(__file__).resolve().parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    logger.info(f"Current working directory: {current_dir}")
    logger.info(f"Python path: {sys.path[:3]}...")  # Show first 3 entries

def check_requirements():
    """Check if required packages are installed"""
    logger.info("Checking required packages...")
    
    required_packages = [
        'django',
        'channels',
        'daphne',
        'channels_redis',
        'redis'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            logger.warning(f"✗ {package} is missing")
    
    if missing_packages:
        logger.error(f"Missing packages: {', '.join(missing_packages)}")
        logger.error("Please install missing packages using:")
        logger.error(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def run_django_setup():
    """Run Django setup and migrations"""
    logger.info("Running Django setup...")
    
    try:
        # Import Django and setup
        import django
        from django.core.management import execute_from_command_line
        from django.conf import settings
        
        django.setup()
        logger.info("✓ Django setup completed")
        
        # Check if we can access the database
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        logger.info("✓ Database connection successful")
        
        # Run migrations
        logger.info("Running database migrations...")
        execute_from_command_line(['manage.py', 'migrate', '--noinput'])
        logger.info("✓ Migrations completed")
        
        # Collect static files
        logger.info("Collecting static files...")
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
        logger.info("✓ Static files collected")
        
        return True
        
    except Exception as e:
        logger.error(f"Django setup failed: {e}")
        return False

def start_daphne_server(port=8000):
    """Start Daphne ASGI server"""
    logger.info(f"Starting Daphne server on port {port}...")
    
    try:
        # Import to verify ASGI application exists
        from public_transport.asgi import application
        logger.info("✓ ASGI application imported successfully")
        
        # Prepare Daphne command
        daphne_cmd = [
            'daphne',
            '-p', str(port),
            '-b', '0.0.0.0',  # Bind to all interfaces for PythonAnywhere
            '--verbosity', '2',  # Increase verbosity for debugging
            'public_transport.asgi:application'
        ]
        
        logger.info(f"Executing command: {' '.join(daphne_cmd)}")
        logger.info("=" * 50)
        logger.info("SERVER STARTING - Press Ctrl+C to stop")
        logger.info("=" * 50)
        
        # Start Daphne server
        subprocess.run(daphne_cmd, check=True)
        
    except KeyboardInterrupt:
        logger.info("\nServer stopped by user")
    except subprocess.CalledProcessError as e:
        logger.error(f"Daphne server failed to start: {e}")
        return False
    except ImportError as e:
        logger.error(f"Failed to import ASGI application: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error starting server: {e}")
        return False
    
    return True

def main():
    """Main function to run the Daphne server"""
    logger.info("=" * 60)
    logger.info("PythonAnywhere Daphne Runner Starting...")
    logger.info("=" * 60)
    
    # Parse command line arguments
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            logger.error(f"Invalid port number: {sys.argv[1]}")
            sys.exit(1)
    
    logger.info(f"Target port: {port}")
    
    # Setup environment
    setup_environment()
    
    # Check requirements
    if not check_requirements():
        logger.error("Requirements check failed. Please install missing packages.")
        sys.exit(1)
    
    # Run Django setup
    if not run_django_setup():
        logger.error("Django setup failed. Please check the logs above.")
        sys.exit(1)
    
    # Start Daphne server
    logger.info("All checks passed. Starting Daphne server...")
    success = start_daphne_server(port)
    
    if not success:
        logger.error("Failed to start Daphne server")
        sys.exit(1)

if __name__ == '__main__':
    main()
