# PythonAnywhere WSGI configuration file
import os
import sys

path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if path not in sys.path:
    sys.path.append(path)

os.environ['DJANGO_SETTINGS_MODULE'] = 'public_transport.settings'

# For HTTP polling fallback
from django.core.wsgi import get_wsgi_application
from whitenoise import WhiteNoise
application = WhiteNoise(get_wsgi_application())
application.add_files('static', prefix='static/')
