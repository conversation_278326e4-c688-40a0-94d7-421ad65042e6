<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع السائقين - عمومي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --border-radius: 12px;
            --border-radius-lg: 16px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
            font-weight: 400;
        }

        .main-container {
            background: var(--background-color);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .page-header {
            background: var(--card-background);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            margin-bottom: 2rem;
            padding: 2rem;
            border: 1px solid var(--border-color);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-align: center;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .page-title i {
            font-size: 2.2rem;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .professional-card {
            background: var(--card-background);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            height: 100%;
        }

        .professional-card:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-2px);
        }

        .card-header-professional {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            padding: 1.5rem;
            border: none;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .card-header-professional i {
            margin-left: 0.5rem;
            font-size: 1.2rem;
        }

        .card-body-professional {
            padding: 1.5rem;
        }

        #map {
            height: 600px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            border: 2px solid var(--border-color);
            transition: all 0.3s ease;
        }

        #map:hover {
            box-shadow: var(--shadow-lg);
        }

        .form-select-professional {
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.75rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            background: var(--card-background);
            color: var(--text-primary);
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
        }

        .form-select-professional:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .alert-professional {
            border: none;
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1rem;
        }

        .alert-success-professional {
            background: linear-gradient(135deg, #dcfdf7, #a7f3d0);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-info-professional {
            background: linear-gradient(135deg, #dbeafe, #93c5fd);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }

        .alert-warning-professional {
            background: linear-gradient(135deg, #fef3c7, #fcd34d);
            color: var(--warning-color);
            border-left: 4px solid var(--warning-color);
        }

        .alert-danger-professional {
            background: linear-gradient(135deg, #fee2e2, #fca5a5);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .driver-card-professional {
            background: var(--card-background);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .driver-card-professional::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, var(--primary-color), var(--primary-dark));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .driver-card-professional:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
            border-color: var(--primary-color);
        }

        .driver-card-professional:hover::before {
            opacity: 1;
        }

        .driver-info h6 {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .driver-info small {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 400;
        }

        .status-badge-online {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.875rem;
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-badge-offline {
            background: linear-gradient(135deg, var(--secondary-color), #94a3b8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.875rem;
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .drivers-count {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.875rem;
            margin-right: 0.5rem;
        }

        .refresh-indicator {
            background: linear-gradient(135deg, #10b981, var(--success-color));
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            text-align: center;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1rem;
        }

        .refresh-indicator i {
            animation: spin 2s linear infinite;
            margin-left: 0.5rem;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .sidebar-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            height: 100%;
        }

        .drivers-list-container {
            max-height: 500px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .drivers-list-container::-webkit-scrollbar {
            width: 6px;
        }

        .drivers-list-container::-webkit-scrollbar-track {
            background: var(--background-color);
            border-radius: 3px;
        }

        .drivers-list-container::-webkit-scrollbar-thumb {
            background: var(--secondary-color);
            border-radius: 3px;
        }

        .drivers-list-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            #map {
                height: 400px;
            }
            
            .professional-card {
                margin-bottom: 1.5rem;
            }
        }

        .leaflet-popup-content-wrapper {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
        }

        .leaflet-popup-content {
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-map-marked-alt"></i>
                    تتبع السائقين (عمومي)
                </h1>
            </div>

            <div class="row g-4">
                <div class="col-lg-8">
                    <div class="professional-card">
                        <div class="card-header-professional">
                            <i class="fas fa-map"></i>
                            خريطة السائقين المباشرة
                        </div>
                        <div class="card-body-professional">
                            <div id="map"></div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="sidebar-container">
                        <div class="professional-card">
                            <div class="card-header-professional">
                                <i class="fas fa-filter"></i>
                                تصفية حسب القرية
                            </div>
                            <div class="card-body-professional">
                                <select id="village-select" class="form-select-professional w-100 mb-3">
                                    <option value="">-- اختر القرية --</option>
                                </select>
                                
                                <div class="refresh-indicator">
                                    <i class="fas fa-sync-alt"></i>
                                    <strong>التحديث التلقائي:</strong> كل 10 ثوان (استطلاع)
                                </div>
                                
                                <div id="status" class="alert-professional alert-info-professional">
                                    <i class="fas fa-info-circle me-2"></i>
                                    اختر قرية لعرض السائقين
                                </div>
                            </div>
                        </div>
                        
                        <div class="professional-card">
                            <div class="card-header-professional">
                                <i class="fas fa-users"></i>
                                قائمة السائقين
                                <span id="drivers-count" class="drivers-count">0</span>
                            </div>
                            <div class="card-body-professional">
                                <div id="drivers-list" class="drivers-list-container">
                                    <!-- Drivers will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
    
    <script>
        let map;
        let markers = [];
        let currentBounds = null;
        let isInitialized = false;

        // Initialize map
        function initializeMap() {
            map = L.map('map').setView([31.9, 35.2], 10);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Store initial bounds
            currentBounds = map.getBounds();
            isInitialized = true;
            console.log('Map initialized');
        }

        // Load villages
        async function loadVillages() {
            try {
                console.log('Loading villages...');
                const response = await fetch('/drivers/api-villages/');
                const villages = await response.json();
                console.log('Villages loaded:', villages);
                
                const select = document.getElementById('village-select');
                select.innerHTML = '<option value="">-- اختر القرية --</option>';
                
                villages.forEach(village => {
                    const option = document.createElement('option');
                    option.value = village.name;
                    option.textContent = `${village.name} - ${village.governorate}`;
                    select.appendChild(option);
                });
                
                updateStatus('تم تحميل القرى بنجاح', 'success');
            } catch (error) {
                console.error('Error loading villages:', error);
                updateStatus('خطأ في تحميل القرى', 'danger');
            }
        }

        // Load drivers for selected village
        async function loadDrivers(village = '') {
            try {
                console.log('Loading drivers for village:', village);
                const url = village ? `/drivers/api-drivers/?village=${encodeURIComponent(village)}` : '/drivers/api-drivers/';
                const response = await fetch(url);
                const drivers = await response.json();
                console.log('Drivers loaded:', drivers);
                
                // Clear existing markers
                markers.forEach(marker => map.removeLayer(marker));
                markers = [];
                
                // Clear drivers list
                const driversList = document.getElementById('drivers-list');
                driversList.innerHTML = '';
                
                if (drivers.length === 0) {
                    updateStatus('لا يوجد سائقين في هذه القرية', 'warning');
                    document.getElementById('drivers-count').textContent = '0';
                    return;
                }
                
                // Add drivers to map and list
                drivers.forEach(driver => {
                    // Add to list
                    const driverCard = document.createElement('div');
                    driverCard.className = 'driver-card-professional';
                    const statusClass = driver.is_on_duty ? 'status-badge-online' : 'status-badge-offline';
                    const statusText = driver.is_on_duty ? 'نشط' : 'غير نشط';
                    const statusIcon = driver.is_on_duty ? 'fas fa-circle' : 'fas fa-circle';
                    const locationText = driver.latitude && driver.longitude ? 
                        `${driver.latitude.toFixed(4)}, ${driver.longitude.toFixed(4)}` : 'لا يوجد موقع';
                    
                    driverCard.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="driver-info">
                                <h6 class="mb-1">${driver.username}</h6>
                                <small class="d-block">${driver.village}</small>
                                <small class="d-block">${driver.phone_number}</small>
                                <small class="d-block">${locationText}</small>
                            </div>
                            <div>
                                <span class="${statusClass}">
                                    <i class="${statusIcon}"></i>${statusText}
                                </span>
                            </div>
                        </div>
                    `;
                    driversList.appendChild(driverCard);
                    
                    // Add to map if has location
                    if (driver.latitude && driver.longitude && driver.is_on_duty) {
                        const marker = L.marker([driver.latitude, driver.longitude])
                            .addTo(map)
                            .bindPopup(`
                                <div style="text-align: center; font-family: 'Cairo', sans-serif;">
                                    <strong style="font-size: 1.1rem; color: #2563eb;">${driver.username}</strong><br>
                                    <small style="color: #64748b;">${driver.village}</small><br>
                                    <small style="color: #64748b;">${driver.phone_number}</small><br>
                                    <span style="background: linear-gradient(135deg, #059669, #10b981); color: white; padding: 0.25rem 0.75rem; border-radius: 50px; font-size: 0.8rem; font-weight: 600;">نشط</span>
                                </div>
                            `);
                        markers.push(marker);
                    }
                });
                
                // Update count and status
                document.getElementById('drivers-count').textContent = drivers.length;
                updateStatus(`تم العثور على ${drivers.length} سائق`, 'success');
                
                // No automatic zooming - user maintains full control over map view
                
            } catch (error) {
                console.error('Error loading drivers:', error);
                updateStatus('خطأ في تحميل السائقين', 'danger');
            }
        }

        // Update status message
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `alert-professional alert-${type}-professional`;
            status.innerHTML = `<i class="fas fa-info-circle me-2"></i> ${message}`;
        }

        // Setup polling for real-time updates (WebSocket disabled for PythonAnywhere compatibility)
        function setupPolling() {
            console.log('Setting up polling for real-time updates...');
            updateStatus('استخدام التحديث بالاستطلاع (متوافق مع PythonAnywhere)', 'info');

            // Poll for location updates every 10 seconds
            setInterval(async () => {
                try {
                    const selectedVillage = document.getElementById('village-select').value;
                    await loadDriversQuietly(selectedVillage);
                } catch (error) {
                    console.error('Polling error:', error);
                }
            }, 10000); // 10 seconds for more frequent updates
        }

        // Load drivers quietly without status updates (for polling)
        async function loadDriversQuietly(village = '') {
            try {
                const url = village ? `/drivers/api-drivers/?village=${encodeURIComponent(village)}` : '/drivers/api-drivers/';
                const response = await fetch(url);
                const drivers = await response.json();

                // Store current driver data for comparison
                const currentDrivers = new Map();
                markers.forEach(marker => {
                    if (marker.options.driverData) {
                        currentDrivers.set(marker.options.driverData.driver_id, marker.options.driverData);
                    }
                });

                // Clear existing markers
                markers.forEach(marker => map.removeLayer(marker));
                markers = [];

                // Update drivers list
                const driversList = document.getElementById('drivers-list');
                driversList.innerHTML = '';

                if (drivers.length === 0) {
                    document.getElementById('drivers-count').textContent = '0';
                    return;
                }

                let hasUpdates = false;

                // Add drivers to map and list
                drivers.forEach(driver => {
                    // Check if this driver's location has changed
                    const previousData = currentDrivers.get(driver.id);
                    if (previousData &&
                        (previousData.latitude !== driver.latitude ||
                         previousData.longitude !== driver.longitude ||
                         previousData.is_on_duty !== driver.is_on_duty)) {
                        hasUpdates = true;
                    }

                    // Add to list
                    const driverCard = document.createElement('div');
                    driverCard.className = 'driver-card-professional';
                    const statusClass = driver.is_on_duty ? 'status-badge-online' : 'status-badge-offline';
                    const statusText = driver.is_on_duty ? 'نشط' : 'غير نشط';
                    const statusIcon = driver.is_on_duty ? 'fas fa-circle' : 'fas fa-circle';
                    const locationText = driver.latitude && driver.longitude ?
                        `${driver.latitude.toFixed(4)}, ${driver.longitude.toFixed(4)}` : 'لا يوجد موقع';

                    driverCard.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="driver-info">
                                <h6 class="mb-1">${driver.username}</h6>
                                <small class="d-block">${driver.village}</small>
                                <small class="d-block">${driver.phone_number}</small>
                                <small class="d-block">${locationText}</small>
                            </div>
                            <div>
                                <span class="${statusClass}">
                                    <i class="${statusIcon}"></i>${statusText}
                                </span>
                            </div>
                        </div>
                    `;
                    driversList.appendChild(driverCard);

                    // Add to map if has location and is on duty
                    if (driver.latitude && driver.longitude && driver.is_on_duty) {
                        const marker = L.marker([driver.latitude, driver.longitude])
                            .addTo(map)
                            .bindPopup(`
                                <div style="text-align: center; font-family: 'Cairo', sans-serif;">
                                    <strong style="font-size: 1.1rem; color: #2563eb;">${driver.username}</strong><br>
                                    <small style="color: #64748b;">${driver.village}</small><br>
                                    <small style="color: #64748b;">${driver.phone_number}</small><br>
                                    <span style="background: linear-gradient(135deg, #059669, #10b981); color: white; padding: 0.25rem 0.75rem; border-radius: 50px; font-size: 0.8rem; font-weight: 600;">نشط</span>
                                </div>
                            `);
                        marker.options.driverData = driver;
                        markers.push(marker);
                    }
                });

                // Update count
                document.getElementById('drivers-count').textContent = drivers.length;

                // Show update notification if there were changes
                if (hasUpdates) {
                    updateStatus('تم تحديث مواقع السائقين', 'success');
                    setTimeout(() => {
                        updateStatus('التحديث التلقائي نشط', 'info');
                    }, 2000);
                }

            } catch (error) {
                console.error('Error in quiet loading:', error);
            }
        }

        // Auto-refresh function (now using polling instead of separate refresh)
        function startAutoRefresh() {
            console.log('Auto-refresh integrated with polling system');
            updateStatus('نظام التحديث التلقائي نشط (كل 10 ثوان)', 'info');
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, initializing...');

            initializeMap();
            loadVillages();
            setupPolling(); // Use polling instead of WebSocket
            startAutoRefresh();

            // Village selection handler
            document.getElementById('village-select').addEventListener('change', function() {
                const selectedVillage = this.value;
                console.log('Village selected:', selectedVillage);
                loadDrivers(selectedVillage, true); // Fit bounds when village is selected
            });

            // Load all drivers initially without changing map view
            loadDrivers('', false);
        });
    </script>
</body>
</html>