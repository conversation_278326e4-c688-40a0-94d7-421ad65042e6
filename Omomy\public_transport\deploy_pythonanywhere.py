#!/usr/bin/env python3
"""
PythonAnywhere Deployment Script (WebSocket Disabled)
====================================================

This script deploys your Django project to PythonAnywhere with WebSocket functionality
disabled, using polling instead for real-time updates. This approach is more reliable
on shared hosting platforms.

Usage:
    python deploy_pythonanywhere.py [port]

Features:
- Disables WebSocket/Channels for better compatibility
- Uses polling for real-time updates
- Optimized for PythonAnywhere shared hosting
- Handles all setup automatically
"""

import os
import sys
import subprocess
import django
from pathlib import Path

def print_banner():
    """Print deployment banner"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        PythonAnywhere Deployment (WebSocket Disabled)       ║
║                                                              ║
║  🚀 Optimized for shared hosting environments               ║
║  📡 Uses polling instead of WebSocket for updates           ║
║  ⚡ Better compatibility and stability                      ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
""")

def setup_environment():
    """Setup environment for deployment"""
    print("\n📋 Setting up environment...")
    
    # Add current directory to Python path
    current_dir = Path(__file__).resolve().parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Set simplified settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings_simple')
    
    print(f"✅ Working directory: {current_dir}")
    print("✅ Using simplified settings (WebSocket disabled)")
    
    return current_dir

def check_dependencies():
    """Check if required dependencies are available"""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'django',
        'djangorestframework',
        'django-cors-headers',
        'whitenoise'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (missing)")
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install them using: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All required dependencies are available")
    return True

def setup_django():
    """Setup Django"""
    print("\n⚙️  Setting up Django...")
    
    try:
        django.setup()
        print("✅ Django setup completed")
        
        # Test database connection
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def run_migrations():
    """Run database migrations"""
    print("\n🗄️  Running database migrations...")
    
    try:
        from django.core.management import execute_from_command_line
        execute_from_command_line(['manage.py', 'migrate', '--noinput'])
        print("✅ Database migrations completed")
        return True
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def collect_static_files():
    """Collect static files"""
    print("\n📁 Collecting static files...")
    
    try:
        from django.core.management import execute_from_command_line
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
        print("✅ Static files collected")
        return True
    except Exception as e:
        print(f"❌ Static file collection failed: {e}")
        return False

def create_startup_script():
    """Create a startup script for easy deployment"""
    print("\n📝 Creating startup script...")
    
    startup_content = """#!/bin/bash
# PythonAnywhere Startup Script
echo "Starting Django project (WebSocket disabled)..."
python run_simple_server.py 8000
"""
    
    with open("start_pythonanywhere.sh", "w") as f:
        f.write(startup_content)
    
    os.chmod("start_pythonanywhere.sh", 0o755)
    print("✅ Created start_pythonanywhere.sh")

def test_application():
    """Test if the application can start"""
    print("\n🧪 Testing application...")
    
    try:
        from django.core.management import execute_from_command_line
        from django.test.utils import get_runner
        from django.conf import settings
        
        # Quick test to see if everything loads
        print("✅ Application can be imported successfully")
        print("✅ Settings are valid")
        print("✅ Ready for deployment")
        return True
    except Exception as e:
        print(f"❌ Application test failed: {e}")
        return False

def print_deployment_instructions():
    """Print final deployment instructions"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    DEPLOYMENT COMPLETE! 🎉                  ║
╚══════════════════════════════════════════════════════════════╝

🚀 Your Django project is ready for PythonAnywhere!

📋 DEPLOYMENT OPTIONS:

1️⃣  Simple Django Server (Recommended):
   python run_simple_server.py 8000

2️⃣  Using manage.py with simplified settings:
   python manage.py runserver 0.0.0.0:8000 --settings=settings_simple

3️⃣  Using the startup script:
   ./start_pythonanywhere.sh

📊 FEATURES ENABLED:
✅ Polling-based real-time updates (every 10 seconds)
✅ Full driver tracking functionality
✅ Village filtering
✅ Map visualization
✅ Admin panel access
✅ REST API endpoints

🔧 WEBSOCKET STATUS:
❌ WebSocket/Channels disabled for better PythonAnywhere compatibility
✅ Using HTTP polling instead (more reliable on shared hosting)

🌐 ACCESS YOUR APPLICATION:
- Main tracking page: http://your-domain.pythonanywhere.com/
- Admin panel: http://your-domain.pythonanywhere.com/admin/
- Driver dashboard: http://your-domain.pythonanywhere.com/driver-dashboard/

⚠️  IMPORTANT NOTES:
- The application uses SQLite database (suitable for development/testing)
- Static files are served from staticfiles/ directory
- Polling updates every 10 seconds instead of real-time WebSocket
- All functionality works the same, just with polling instead of WebSocket

🎯 NEXT STEPS:
1. Upload your project to PythonAnywhere
2. Run this deployment script
3. Start the server using one of the methods above
4. Access your application in the browser

Happy deploying! 🚀
""")

def main():
    """Main deployment function"""
    print_banner()
    
    # Parse command line arguments
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"❌ Invalid port number: {sys.argv[1]}")
            sys.exit(1)
    
    # Run deployment steps
    current_dir = setup_environment()
    
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        sys.exit(1)
    
    if not setup_django():
        print("\n❌ Django setup failed. Please check the errors above.")
        sys.exit(1)
    
    if not run_migrations():
        print("\n❌ Migration failed. Please check the errors above.")
        sys.exit(1)
    
    if not collect_static_files():
        print("\n❌ Static file collection failed. Please check the errors above.")
        sys.exit(1)
    
    create_startup_script()
    
    if not test_application():
        print("\n❌ Application test failed. Please check the errors above.")
        sys.exit(1)
    
    print_deployment_instructions()

if __name__ == '__main__':
    main()
