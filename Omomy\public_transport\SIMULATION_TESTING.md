# Driver Movement Simulation Testing

This document explains how to test the driver movement simulation functionality with 10-second intervals.

## Overview

The driver movement simulation has been updated to move the driver's location every **10 seconds** instead of the previous 5 seconds. This allows for more realistic testing of the location tracking system.

## Features

- **10-second interval updates**: Driver location is updated every 10 seconds during simulation
- **Random movement**: Driver moves randomly within a 500-meter radius of the starting position
- **Real-time updates**: Location changes are sent via WebSocket for real-time tracking
- **Visual feedback**: Map shows the driver's movement in real-time
- **Location history**: All movements are stored in the database for tracking

## How to Test

### Method 1: Using the Driver Dashboard

1. **Login as a driver**:
   - Go to `/drivers/login/`
   - Use your driver credentials

2. **Start duty**:
   - Click "بدء الدوام" (Start Duty) button
   - Allow location access when prompted

3. **Start simulation**:
   - Scroll down to the "محاكاة الحركة" (Movement Simulation) section
   - Click "بدء محاكاة الحركة" (Start Movement Simulation)
   - The simulation will start moving your location every 10 seconds

4. **Monitor the simulation**:
   - Watch the map marker move every 10 seconds
   - Check the browser console for movement logs
   - Location updates are sent to the server automatically

5. **Stop simulation**:
   - Click "إيقاف المحاكاة" (Stop Simulation) to end the test

### Method 2: Using the Test Page

1. **Access the test page**:
   - Go to `/drivers/simulation-test/`
   - This is a dedicated testing interface

2. **Use the test interface**:
   - Click "بدء المحاكاة" (Start Simulation)
   - Monitor the movement counter and log
   - Watch the map for real-time movement
   - Click "إيقاف المحاكاة" (Stop Simulation) when done

### Method 3: Using the Python Test Script

1. **Run the test script**:
   ```bash
   cd /path/to/public_transport
   python test_driver_simulation.py
   ```

2. **Follow the prompts**:
   - The script will create a test driver if needed
   - Choose to run the simulation
   - Set the duration (default: 2 minutes)
   - Watch the console output for movement updates

## Technical Details

### Simulation Parameters

- **Update Interval**: 10 seconds (10,000 milliseconds)
- **Movement Radius**: 500 meters from starting position
- **Coordinate Precision**: 6 decimal places
- **Movement Pattern**: Random within radius

### Code Locations

The simulation functionality is implemented in:

- **Frontend**: `drivers/templates/drivers/dashboard.html`
  - Lines 414-420: Main simulation interval
  - Lines 526-540: Alternative simulation implementation

- **Backend**: Location updates are handled by:
  - `drivers/views.py`: `update_driver_location()` function
  - `drivers/consumers.py`: WebSocket location updates
  - `tracking/models.py`: Location history storage

### Database Storage

Each simulated movement creates:
1. **Driver record update**: Updates `current_latitude`, `current_longitude`, and `last_location_update`
2. **Location history**: Creates a new `Location` record with timestamp

## Monitoring and Debugging

### Browser Console

Check the browser console for simulation logs:
```javascript
// Expected console output every 10 seconds:
"Generated new location: {latitude: 31.904567, longitude: 35.203891}"
"Movement #1: 31.904567, 35.203891"
```

### Database Queries

Check location history in Django admin or database:
```sql
SELECT * FROM tracking_location 
WHERE driver_id = [driver_id] 
ORDER BY timestamp DESC 
LIMIT 10;
```

### WebSocket Monitoring

Monitor WebSocket messages in browser DevTools:
- Network tab → WS (WebSocket)
- Look for location update messages every 10 seconds

## Troubleshooting

### Common Issues

1. **Simulation not starting**:
   - Ensure driver is on duty
   - Check browser location permissions
   - Verify JavaScript console for errors

2. **No location updates**:
   - Check WebSocket connection status
   - Verify CSRF token is valid
   - Check server logs for errors

3. **Map not updating**:
   - Ensure Leaflet.js is loaded
   - Check map initialization
   - Verify marker creation

### Performance Considerations

- **Database growth**: Simulation creates many location records
- **WebSocket connections**: Monitor concurrent connections
- **Browser resources**: Long simulations may consume memory

## Configuration

To change the simulation interval, modify these values in `dashboard.html`:

```javascript
// Change from 10000 (10 seconds) to desired interval
simulationInterval = setInterval(() => {
    // simulation code
}, 10000); // ← Change this value
```

## Testing Checklist

- [ ] Driver can start/stop duty
- [ ] Simulation starts when button is clicked
- [ ] Location updates every 10 seconds
- [ ] Map marker moves with each update
- [ ] WebSocket sends location data
- [ ] Database stores location history
- [ ] Simulation can be stopped
- [ ] Multiple drivers can simulate simultaneously

## Notes

- The simulation uses the browser's geolocation API as a starting point
- Movement is generated mathematically, not based on real GPS
- For production use, disable or remove simulation features
- Consider adding rate limiting for location updates in production
