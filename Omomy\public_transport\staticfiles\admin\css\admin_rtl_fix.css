/* Custom RTL Admin Styling for Arabic */

/* Fix header alignment */
#header {
    direction: rtl;
    text-align: right;
}

#branding h1 {
    text-align: right;
    direction: rtl;
}

/* Fix navigation */
#nav-global {
    direction: rtl;
}

#nav-global li {
    float: right;
    margin-left: 0;
    margin-right: 20px;
}

/* Fix breadcrumbs */
.breadcrumbs {
    direction: rtl;
    text-align: right;
}

/* Fix form fields */
.form-row {
    direction: rtl;
}

.form-row label {
    float: right;
    text-align: right;
    margin-left: 10px;
    margin-right: 0;
}

/* Fix table headers */
#changelist table thead th {
    text-align: right;
    direction: rtl;
}

#changelist table tbody td {
    text-align: right;
    direction: rtl;
}

/* Fix action buttons */
.actions {
    direction: rtl;
    text-align: right;
}

.actions select {
    margin-left: 10px;
    margin-right: 0;
}

/* Fix pagination */
.paginator {
    direction: rtl;
    text-align: right;
}

.paginator a, .paginator span {
    float: right;
    margin-left: 5px;
    margin-right: 0;
}

/* Fix filters */
#changelist-filter {
    direction: rtl;
    right: auto;
    left: 0;
}

#changelist-filter h2 {
    text-align: right;
}

#changelist-filter h3 {
    text-align: right;
}

/* Fix search */
#changelist-search {
    direction: rtl;
}

#searchbar {
    direction: rtl;
    text-align: right;
}

/* Fix fieldsets */
.module h2, .module caption, .inline-group h2 {
    text-align: right;
    direction: rtl;
}

fieldset.module {
    direction: rtl;
}

/* Fix inline forms */
.inline-group .tabular {
    direction: rtl;
}

.inline-group .tabular th {
    text-align: right;
}

.inline-group .tabular td {
    text-align: right;
}

/* Fix buttons */
.default, input[type=submit].default, .button.default {
    margin-left: 10px;
    margin-right: 0;
}

/* Fix dashboard */
#content-main {
    direction: rtl;
}

.dashboard .module {
    direction: rtl;
}

.dashboard .module h2 {
    text-align: right;
}

.dashboard .module table {
    direction: rtl;
}

.dashboard .module table th {
    text-align: right;
}

.dashboard .module table td {
    text-align: right;
}

/* Fix change form */
.change-form .form-row {
    direction: rtl;
}

.change-form .form-row label {
    float: right;
    text-align: right;
}

/* Fix help text */
.help {
    direction: rtl;
    text-align: right;
}

/* Fix error messages */
.errorlist {
    direction: rtl;
    text-align: right;
}

.errorlist li {
    text-align: right;
}

/* Fix success messages */
.messagelist {
    direction: rtl;
}

.messagelist li {
    text-align: right;
}

/* Fix date/time widgets */
.datetime input {
    direction: ltr;
}

.date input {
    direction: ltr;
}

.time input {
    direction: ltr;
}

/* Fix select widgets */
select {
    direction: rtl;
}

/* Fix checkbox and radio alignment */
.checkbox-row, .radio-row {
    direction: rtl;
}

.checkbox-row label, .radio-row label {
    float: none;
    margin-right: 5px;
    margin-left: 0;
}

/* Fix related widget lookups */
.related-widget-wrapper {
    direction: rtl;
}

/* Custom styling for better appearance */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

#header {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    border-bottom: 3px solid #3498db;
}

#branding h1 {
    color: #ffffff;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.module h2, .module caption, .inline-group h2 {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    color: #ffffff;
    border-radius: 4px 4px 0 0;
    padding: 10px 15px;
    margin: 0;
    font-weight: 500;
}

.button, input[type=submit], input[type=button], .submit-row input {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    border-radius: 4px;
    color: #ffffff;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover {
    background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.default, input[type=submit].default, .button.default {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.default:hover, input[type=submit].default:hover, .button.default:hover {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

/* Fix table styling */
#changelist table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

#changelist table thead th {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    color: #ffffff;
    font-weight: 500;
    padding: 12px 15px;
}

#changelist table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

#changelist table tbody tr:hover {
    background-color: #e3f2fd;
}

/* Fix form styling */
.form-row input, .form-row select, .form-row textarea {
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    transition: border-color 0.3s ease;
}

.form-row input:focus, .form-row select:focus, .form-row textarea:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}
