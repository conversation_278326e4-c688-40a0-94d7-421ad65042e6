
# drivers/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('api/drivers_by_town/<int:town_id>/', views.get_drivers_by_town, name='get_drivers_by_town'),
    path('api-villages/', views.get_villages, name='get_villages'),
    path('login/', views.driver_login, name='driver_login'),
    path('dashboard/', views.driver_dashboard, name='driver_dashboard'),
    path('logout/', views.driver_logout, name='driver_logout'),
    path('toggle-duty/', views.toggle_duty_status, name='toggle_duty_status'),
    path('update-location/', views.update_driver_location, name='update_driver_location'),
    path('public/', views.public_driver_view, name='public_driver_view'),
    path('api-drivers/', views.api_drivers, name='api_drivers'),

]