# public_transport/urls.py
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic.base import RedirectView

# Admin site customization
admin.site.site_header = "نظام إدارة النقل العام"
admin.site.site_title = "إدارة النقل العام"
admin.site.index_title = "لوحة التحكم الرئيسية"

urlpatterns = [
    path('admin/', admin.site.urls),
    path('tracking/', include('tracking.urls')),
    path('drivers/', include('drivers.urls')),
    path('', RedirectView.as_view(url='/drivers/public/'), name='home'),
]

# Serve static files during development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

