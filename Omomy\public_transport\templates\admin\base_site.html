{% extends "admin/base.html" %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% load static %}

{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/admin_rtl_fix.css' %}">
    <style>
        /* Additional inline styles for immediate fixes */
        body {
            direction: rtl;
            text-align: right;
        }
        
        #header {
            direction: rtl;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            border-bottom: 3px solid #3498db;
        }
        
        #branding h1, #branding h1 a:link, #branding h1 a:visited {
            color: #ffffff;
            text-align: right;
            direction: rtl;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        #user-tools {
            direction: rtl;
            text-align: right;
            float: left;
        }
        
        #user-tools a {
            color: #ffffff;
            margin-left: 10px;
            margin-right: 0;
        }
        
        .breadcrumbs {
            direction: rtl;
            text-align: right;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        
        .breadcrumbs a {
            color: #3498db;
        }
        
        #content {
            direction: rtl;
        }
        
        #content-main {
            direction: rtl;
            float: right;
        }
        
        #content-related {
            direction: rtl;
            float: left;
        }
        
        .dashboard .module {
            direction: rtl;
        }
        
        .dashboard .module h2 {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: #ffffff;
            text-align: right;
            direction: rtl;
            border-radius: 4px 4px 0 0;
            padding: 10px 15px;
            margin: 0;
            font-weight: 500;
        }
        
        .dashboard .module table {
            direction: rtl;
        }
        
        .dashboard .module table th,
        .dashboard .module table td {
            text-align: right;
            direction: rtl;
        }
        
        /* Fix form elements */
        .form-row {
            direction: rtl;
        }
        
        .form-row label {
            float: right;
            text-align: right;
            margin-left: 10px;
            margin-right: 0;
        }
        
        /* Fix buttons */
        .button, input[type=submit], input[type=button], .submit-row input {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: 4px;
            color: #ffffff;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-left: 5px;
            margin-right: 0;
        }
        
        .button:hover, input[type=submit]:hover, input[type=button]:hover {
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .default, input[type=submit].default, .button.default {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .default:hover, input[type=submit].default:hover, .button.default:hover {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }
        
        /* Fix tables */
        #changelist table {
            direction: rtl;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #changelist table thead th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: #ffffff;
            text-align: right;
            direction: rtl;
            font-weight: 500;
            padding: 12px 15px;
        }
        
        #changelist table tbody td {
            text-align: right;
            direction: rtl;
            padding: 10px 15px;
        }
        
        #changelist table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        #changelist table tbody tr:hover {
            background-color: #e3f2fd;
        }
        
        /* Fix filters */
        #changelist-filter {
            direction: rtl;
            right: auto;
            left: 0;
        }
        
        #changelist-filter h2,
        #changelist-filter h3 {
            text-align: right;
            direction: rtl;
        }
        
        /* Fix search */
        #changelist-search {
            direction: rtl;
        }
        
        #searchbar {
            direction: rtl;
            text-align: right;
        }
        
        /* Fix pagination */
        .paginator {
            direction: rtl;
            text-align: right;
        }
        
        .paginator a, .paginator span {
            float: right;
            margin-left: 5px;
            margin-right: 0;
        }
        
        /* Fix actions */
        .actions {
            direction: rtl;
            text-align: right;
        }
        
        .actions select {
            margin-left: 10px;
            margin-right: 0;
        }
        
        /* Fix messages */
        .messagelist {
            direction: rtl;
        }
        
        .messagelist li {
            text-align: right;
            direction: rtl;
        }
        
        /* Fix fieldsets */
        fieldset.module {
            direction: rtl;
        }
        
        .module h2, .module caption, .inline-group h2 {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: #ffffff;
            text-align: right;
            direction: rtl;
            border-radius: 4px 4px 0 0;
            padding: 10px 15px;
            margin: 0;
            font-weight: 500;
        }
        
        /* Custom enhancements */
        .object-tools {
            direction: rtl;
            float: left;
        }
        
        .object-tools li {
            float: right;
            margin-left: 5px;
            margin-right: 0;
        }
        
        /* Fix inline forms */
        .inline-group .tabular {
            direction: rtl;
        }
        
        .inline-group .tabular th,
        .inline-group .tabular td {
            text-align: right;
        }
    </style>
{% endblock %}

{% block branding %}
<h1 id="site-name"><a href="{% url 'admin:index' %}">{{ site_header|default:_('Django administration') }}</a></h1>
{% endblock %}

{% block nav-global %}{% endblock %}
