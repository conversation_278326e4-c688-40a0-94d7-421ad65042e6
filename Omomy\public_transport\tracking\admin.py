from django.contrib import admin
from django.utils.html import format_html
from .models import Location

@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ('driver_info', 'location_coords', 'formatted_timestamp', 'time_ago')
    list_filter = ('driver', 'timestamp')
    search_fields = ('driver__username', 'driver__phone_number')
    readonly_fields = ('timestamp',)
    list_per_page = 50
    ordering = ('-timestamp',)

    def driver_info(self, obj):
        return format_html(
            '<strong>{}</strong><br><small style="color: gray;">{}</small>',
            obj.driver.username,
            obj.driver.phone_number
        )
    driver_info.short_description = 'معلومات السائق'

    def location_coords(self, obj):
        try:
            lat = float(obj.latitude) if obj.latitude is not None else 0.0
            lng = float(obj.longitude) if obj.longitude is not None else 0.0
            return format_html(
                '<span style="font-family: monospace; background: #f0f0f0; padding: 2px 6px; border-radius: 3px;">{:.6f}, {:.6f}</span>',
                lat, lng
            )
        except (ValueError, TypeError):
            return format_html('<span style="color: red;">خطأ في الإحداثيات</span>')
    location_coords.short_description = 'الإحداثيات'

    def formatted_timestamp(self, obj):
        try:
            formatted_time = obj.timestamp.strftime('%Y-%m-%d %H:%M:%S')
            return format_html(
                '<span style="font-size: 12px;">{}</span>',
                formatted_time
            )
        except (AttributeError, ValueError):
            return format_html('<span style="color: red;">خطأ في التاريخ</span>')
    formatted_timestamp.short_description = 'التاريخ والوقت'

    def time_ago(self, obj):
        from django.utils import timezone
        import datetime

        now = timezone.now()
        diff = now - obj.timestamp

        if diff.days > 0:
            return format_html('<span style="color: red;">منذ {} يوم</span>', diff.days)
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return format_html('<span style="color: orange;">منذ {} ساعة</span>', hours)
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return format_html('<span style="color: blue;">منذ {} دقيقة</span>', minutes)
        else:
            return format_html('<span style="color: green;">الآن</span>')
    time_ago.short_description = 'منذ متى'

    class Meta:
        verbose_name = "موقع السائق"
        verbose_name_plural = "مواقع السائقين"
