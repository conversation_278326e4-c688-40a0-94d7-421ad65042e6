import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Driver

logger = logging.getLogger(__name__)

class DriverLocationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # Check if user is authenticated and is a driver
        user = self.scope.get("user")
        if not user or user.is_anonymous:
            logger.warning("Unauthenticated user attempted to connect to DriverLocationConsumer")
            await self.close()
            return

        # Check if user is a Driver instance
        if not hasattr(user, 'is_on_duty'):
            logger.warning(f"Non-driver user {user.username} attempted to connect to DriverLocationConsumer")
            await self.close()
            return

        self.driver_group_name = f'driver_{user.id}'

        # Join driver group
        await self.channel_layer.group_add(
            self.driver_group_name,
            self.channel_name
        )

        logger.info(f"Driver {user.username} connected to location WebSocket")
        await self.accept()

    async def disconnect(self, close_code):
        # Leave driver group if it exists
        if hasattr(self, 'driver_group_name'):
            await self.channel_layer.group_discard(
                self.driver_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        user = self.scope.get("user")
        if not user or user.is_anonymous:
            return

        try:
            data = json.loads(text_data)
            latitude = data.get('latitude')
            longitude = data.get('longitude')

            if latitude is None or longitude is None:
                return

            # Send to public group
            await self.channel_layer.group_send(
                'public_location_updates',
                {
                    'type': 'location_update',
                    'driver_id': str(user.id),
                    'username': user.username,
                    'phone_number': getattr(user, 'phone_number', ''),
                    'village': str(user.village) if user.village else '',
                    'latitude': latitude,
                    'longitude': longitude
                }
            )

            # Update driver location in database
            await self.update_driver_location(
                user.id,
                latitude,
                longitude
            )
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Error processing location data from driver {user.username}: {e}")
            pass

    @database_sync_to_async
    def update_driver_location(self, driver_id, latitude, longitude):
        from django.utils import timezone
        Driver.objects.filter(id=driver_id).update(
            current_latitude=latitude,
            current_longitude=longitude,
            last_location_update=timezone.now()
        )

class PublicLocationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        village = self.scope['url_route']['kwargs'].get('village')
        if village:
            self.village_group_name = f'location_updates_{village}'
        else:
            self.village_group_name = 'public_location_updates'
            
        await self.channel_layer.group_add(
            self.village_group_name,
            self.channel_name
        )
        
        await self.accept()

    async def disconnect(self, close_code):
        if hasattr(self, 'village_group_name'):
            await self.channel_layer.group_discard(
                self.village_group_name,
                self.channel_name
            )
        
    async def location_update(self, event):
        await self.send(text_data=json.dumps({
            'driver_id': event['driver_id'],
            'username': event['username'],
            'phone_number': event['phone_number'],
            'village': event['village'],
            'latitude': event['latitude'],
            'longitude': event['longitude']
        }))
