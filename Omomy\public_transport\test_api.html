<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
    async function testAPI() {
        try {
            console.log('Testing API...');
            const response = await fetch('/drivers/api-drivers/?village=كفرعين');
            const data = await response.json();
            
            console.log('API Response:', data);
            console.log('Data type:', typeof data);
            console.log('Is array:', Array.isArray(data));
            console.log('Length:', data.length);
            
            if (data.length > 0) {
                const driver = data[0];
                console.log('First driver:', driver);
                console.log('Driver latitude:', driver.latitude, 'type:', typeof driver.latitude);
                console.log('Driver longitude:', driver.longitude, 'type:', typeof driver.longitude);
                console.log('Driver is_on_duty:', driver.is_on_duty, 'type:', typeof driver.is_on_duty);
                
                // Test marker creation logic
                if (driver.latitude && driver.longitude && driver.is_on_duty) {
                    console.log('✅ Driver should appear on map!');
                } else {
                    console.log('❌ Driver will NOT appear on map');
                    console.log('  - Has latitude:', !!driver.latitude);
                    console.log('  - Has longitude:', !!driver.longitude);
                    console.log('  - Is on duty:', !!driver.is_on_duty);
                }
            }
            
            document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('result').innerHTML = 'Error: ' + error.message;
        }
    }
    </script>
</body>
</html>
