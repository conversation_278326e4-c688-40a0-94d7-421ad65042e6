from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from drivers.models import Village
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from django.http import JsonResponse
from tracking.models import Location
from drivers.models import Driver

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

def get_drivers_by_town(request, town_id):
    try:
        drivers = Driver.objects.filter(village_id=town_id, is_on_duty=True)
        data = []
        for driver in drivers:
            driver_data = {
                'id': driver.id,
                'username': driver.username,
                'phone_number': driver.phone_number,
                'village': str(driver.village),
                'latitude': str(driver.current_latitude) if driver.current_latitude else None,
                'longitude': str(driver.current_longitude) if driver.current_longitude else None,
                'last_update': driver.last_location_update.isoformat() if driver.last_location_update else None,
                'is_on_duty': driver.is_on_duty
            }
            data.append(driver_data)
        return JsonResponse(data, safe=False)
    except Exception as e:
        print(f"Error in get_drivers_by_town: {e}")
        return JsonResponse({'error': str(e)}, status=400)

def get_villages(request):
    villages = Village.objects.all().order_by('name')
    print(f"Total villages found: {villages.count()}")  # Debug log
    
    village_list = []
    for village in villages:
        village_list.append({
            'name': village.name,
            'governorate': village.governorate
        })
        print(f"Added village: {village.name} - {village.governorate}")  # Debug log
    
    return JsonResponse(village_list, safe=False)

def driver_login(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        if not username or not password:
            messages.error(request, 'يرجى إدخال اسم المستخدم وكلمة المرور')
            return render(request, 'drivers/login.html', {'title': 'تسجيل الدخول'})
            
        user = authenticate(request, username=username, password=password)
        
        if user is None:
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')
            print(f"Failed login for username: {username}")  # Debug output
        elif not isinstance(user, Driver):
            messages.error(request, 'هذا الحساب ليس حساب سائق صالح')
            print(f"Non-driver account tried to login: {username}")  # Debug output
        else:
            login(request, user)
            request.session['driver_id'] = user.id
            print(f"Successful login for driver: {username}")  # Debug output
            return redirect('driver_dashboard')
    
    return render(request, 'drivers/login.html', {'title': 'تسجيل الدخول'})

@login_required 
def driver_dashboard(request):
    if not isinstance(request.user, Driver):
        return redirect('admin:login')
    
    selected_village = request.GET.get('village')
    villages = Driver.objects.values('village').distinct()
    
    if selected_village:
        drivers = Driver.objects.filter(village__name=selected_village)
    else:
        drivers = Driver.objects.none()

    return render(request, 'drivers/dashboard.html', {
        'title': 'لوحة التحكم',
        'driver': request.user,
        'villages': villages,
        'selected_village': selected_village,
        'drivers': drivers
    })

@login_required
def toggle_duty_status(request):
    if request.method == 'POST' and isinstance(request.user, Driver):
        driver = request.user
        driver.is_on_duty = not driver.is_on_duty
        
        # If starting shift, save current location if provided
        if driver.is_on_duty and request.POST.get('latitude') and request.POST.get('longitude'):
            driver.current_latitude = request.POST['latitude']
            driver.current_longitude = request.POST['longitude']
            driver.last_location_update = timezone.now()
            driver.tracking_paused = False
        else:
            # When stopping duty, pause background tracking
            driver.tracking_paused = True
        
        driver.save()
        return JsonResponse({
            'status': 'success', 
            'is_on_duty': driver.is_on_duty,
            'latitude': str(driver.current_latitude) if driver.current_latitude else None,
            'longitude': str(driver.current_longitude) if driver.current_longitude else None
        })
    return JsonResponse({'status': 'error'}, status=400)

def driver_logout(request):
    logout(request)
    return redirect('driver_login')

@csrf_exempt
@login_required
def update_driver_location(request):
    if request.method == 'POST':
        latitude = request.POST.get('latitude')
        longitude = request.POST.get('longitude')

        if not latitude or not longitude:
            return JsonResponse({'status': 'error', 'message': 'Missing latitude or longitude'})

        try:
            # Validate latitude and longitude
            lat_val = float(latitude)
            lng_val = float(longitude)

            if not (-90 <= lat_val <= 90) or not (-180 <= lng_val <= 180):
                return JsonResponse({'status': 'error', 'message': 'Invalid coordinates'})

            driver = request.user

            # Update driver's current location in Driver model
            driver.current_latitude = lat_val
            driver.current_longitude = lng_val
            driver.last_location_update = timezone.now()
            driver.save()

            # Also create a location history record
            Location.objects.create(driver=driver, latitude=lat_val, longitude=lng_val)

            return JsonResponse({'status': 'success'})
        except (ValueError, TypeError):
            return JsonResponse({'status': 'error', 'message': 'Invalid coordinate format'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})
    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})

def public_driver_view(request):
    """Public driver view - now using the simplified version"""
    return render(request, 'drivers/public_view_simple.html', {
        'title': 'تتبع السائقين'
    })

from django.utils import timezone
from datetime import timedelta
from urllib.request import urlopen
import json

def reverse_geocode(lat, lng):
    try:
        url = f"https://nominatim.openstreetmap.org/reverse?format=json&lat={lat}&lon={lng}&zoom=16&accept-language=ar"
        with urlopen(url) as response:
            data = json.load(response)
            return data.get('display_name', '')
    except Exception as e:
        print(f"Geocoding error: {e}")
        return ''

def api_drivers(request):
    village = request.GET.get('village')
    print(f"API called with village param: {village}")  # Debug log
    
    if village:
        drivers = Driver.objects.filter(village__name=village)
        print(f"Filtering drivers for village: {village}. Found {drivers.count()} drivers")  # Debug
    else:
        drivers = Driver.objects.all()
        print(f"Showing all drivers. Total count: {drivers.count()}")  # Debug
    
    data = []
    active_with_location = 0
    active_without_location = 0
    
    for driver in drivers:
        if driver.is_on_duty:
            # Get most recent location regardless of time (but prefer recent ones)
            time_threshold = timezone.now() - timedelta(minutes=60)
            location = (Location.objects.filter(driver=driver)
                        .order_by('-timestamp')
                        .first())
            
            recent = location and location.timestamp >= time_threshold
            
            location_name = ''
            if driver.current_latitude and driver.current_longitude:
                location_name = reverse_geocode(
                    float(driver.current_latitude),
                    float(driver.current_longitude)
                )
            
            # Show all drivers with their status
            driver_data = {
                'username': driver.username,
                'village': str(driver.village),
                'phone_number': driver.phone_number,
                'is_on_duty': driver.is_on_duty,
                'latitude': float(driver.current_latitude) if driver.current_latitude else None,
                'longitude': float(driver.current_longitude) if driver.current_longitude else None,
                'is_recent': driver.last_location_update and driver.current_latitude and driver.current_longitude and (
                    (timezone.now() - driver.last_location_update) < timedelta(minutes=15)
                ),
                'last_location_update': driver.last_location_update.isoformat() if driver.last_location_update else None,
                'location_name': location_name.split(',')[0] if location_name else None
            }
            print(f"Driver {driver.username} - location data:", driver_data)  # Debug
            data.append(driver_data)
            if driver.current_latitude and driver.current_longitude:
                active_with_location += 1
            else:
                active_without_location += 1
    
    print(f"Active drivers: {len(data)} (with location: {active_with_location}, without: {active_without_location})")  # Debug
    
    if len(data) == 0:
        # Check if village has any drivers (active or inactive)
        has_drivers = Driver.objects.filter(village__name=village).exists()
        
        if has_drivers:
            # Return all drivers from this village (including inactive ones)
            drivers = Driver.objects.filter(village__name=village)
            data = []
            for driver in drivers:
                driver_data = {
                    'username': driver.username,
                    'village': str(driver.village),
                    'phone_number': driver.phone_number,
                    'is_on_duty': driver.is_on_duty,
                    'latitude': float(driver.current_latitude) if driver.current_latitude else None,
                    'longitude': float(driver.current_longitude) if driver.current_longitude else None,
                    'is_recent': driver.last_location_update and (
                        (timezone.now() - driver.last_location_update) < timedelta(minutes=15)
                    ),
                    'last_location_update': driver.last_location_update.isoformat() if driver.last_location_update else None,
                    'location_name': reverse_geocode(float(driver.current_latitude), float(driver.current_longitude)) if driver.current_latitude and driver.current_longitude else ''
                }
                print(f"Driver {driver.username} - location data: {driver_data}")
                data.append(driver_data)
        else:
            # Village has no drivers at all
            return JsonResponse({
                'message': 'لا يوجد سائقين على النظام لهذه القرية',
                'has_drivers': False
            }, safe=False)
    
    return JsonResponse(data, safe=False)


