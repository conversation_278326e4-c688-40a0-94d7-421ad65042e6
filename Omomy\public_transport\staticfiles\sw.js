const CACHE_NAME = 'driver-tracker-v1';
const API_URL = '/drivers/update-location/';
const TOGGLE_URL = '/drivers/toggle-duty/';

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll([
        '/',
        '/static/drivers/js/app.js',
        '/static/drivers/css/styles.css'
      ]))
  );
});

self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cache => {
          if (cache !== CACHE_NAME) {
            return caches.delete(cache);
          }
        })
      );
    })
  );
});

self.addEventListener('backgroundfetchsuccess', (event) => {
  const received = event.fetches.map(fetch => fetch.response);
  console.log('[SW] Background fetch data:', received);
});

self.addEventListener('sync', (event) => {
  if (event.tag === 'update-location') {
    event.waitUntil(
      navigator.permissions.query({name: 'geolocation'})
        .then(permissionStatus => {
          if (permissionStatus.state === 'granted') {
            return navigator.geolocation.getCurrentPosition(position => {
              const {latitude, longitude} = position.coords;
              return fetch(API_URL, {
                method: 'POST',
                body: JSON.stringify({latitude, longitude}),
                headers: {
                  'Content-Type': 'application/json',
                },
              });
            });
          }
        })
    );
  }
});
