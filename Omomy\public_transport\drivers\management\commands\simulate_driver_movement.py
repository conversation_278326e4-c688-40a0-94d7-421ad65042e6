import time
import random
import math
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils import timezone
from drivers.models import Driver, Village
from tracking.models import Location
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync


class Command(BaseCommand):
    help = 'Simulate driver movement by cycling through available drivers every 10 seconds'

    def add_arguments(self, parser):
        parser.add_argument(
            '--duration',
            type=int,
            default=300,  # 5 minutes default
            help='Duration of simulation in seconds (default: 300)'
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=10,
            help='Interval between movements in seconds (default: 10)'
        )
        parser.add_argument(
            '--radius',
            type=float,
            default=0.005,  # ~500 meters
            help='Movement radius in decimal degrees (default: 0.005)'
        )
        parser.add_argument(
            '--create-test-drivers',
            action='store_true',
            help='Create test drivers if none exist'
        )

    def handle(self, *args, **options):
        duration = options['duration']
        interval = options['interval']
        radius = options['radius']
        create_test = options['create_test_drivers']

        self.stdout.write(
            self.style.SUCCESS(
                f'Starting driver movement simulation for {duration} seconds '
                f'with {interval}s intervals'
            )
        )

        # Create test drivers if requested and none exist
        if create_test:
            self.create_test_drivers()

        # Get available drivers
        drivers = list(Driver.objects.filter(is_active=True))
        if not drivers:
            self.stdout.write(
                self.style.ERROR('No active drivers found. Use --create-test-drivers to create some.')
            )
            return

        self.stdout.write(f'Found {len(drivers)} active drivers')

        # Initialize starting positions for drivers
        self.initialize_driver_positions(drivers)

        # Set up WebSocket channel layer
        channel_layer = get_channel_layer()

        # Simulation loop
        start_time = time.time()
        driver_index = 0
        movement_count = 0

        try:
            while time.time() - start_time < duration:
                current_driver = drivers[driver_index]
                
                # Generate new position
                new_lat, new_lng = self.generate_new_position(
                    current_driver, radius
                )
                
                # Update driver location
                self.update_driver_location(current_driver, new_lat, new_lng)
                
                # Send WebSocket update
                if channel_layer:
                    self.send_websocket_update(
                        channel_layer, current_driver, new_lat, new_lng
                    )
                
                movement_count += 1
                self.stdout.write(
                    f'Movement #{movement_count}: {current_driver.username} '
                    f'moved to {new_lat:.6f}, {new_lng:.6f}'
                )
                
                # Move to next driver
                driver_index = (driver_index + 1) % len(drivers)
                
                # Wait for next interval
                time.sleep(interval)

        except KeyboardInterrupt:
            self.stdout.write(self.style.WARNING('\nSimulation interrupted by user'))

        self.stdout.write(
            self.style.SUCCESS(
                f'Simulation completed. Total movements: {movement_count}'
            )
        )

    def create_test_drivers(self):
        """Create test drivers for simulation"""
        # Create test village if it doesn't exist
        village, created = Village.objects.get_or_create(
            name='Test Village',
            defaults={'governorate': 'Test Governorate'}
        )
        
        test_drivers_data = [
            {'username': 'driver1', 'phone': '0599111111'},
            {'username': 'driver2', 'phone': '0599222222'},
            {'username': 'driver3', 'phone': '0599333333'},
            {'username': 'driver4', 'phone': '0599444444'},
            {'username': 'driver5', 'phone': '0599555555'},
        ]
        
        created_count = 0
        for driver_data in test_drivers_data:
            driver, created = Driver.objects.get_or_create(
                username=driver_data['username'],
                defaults={
                    'phone_number': driver_data['phone'],
                    'village': village,
                    'is_active': True,
                    'is_on_duty': True,
                }
            )
            if created:
                driver.set_password('test123')
                driver.save()
                created_count += 1
        
        self.stdout.write(f'Created {created_count} test drivers')

    def initialize_driver_positions(self, drivers):
        """Initialize starting positions for drivers around Palestine"""
        # Base coordinates for different Palestinian cities
        base_locations = [
            (31.9046, 35.2066),  # Jerusalem
            (32.2211, 35.2544),  # Nablus
            (31.7056, 35.2056),  # Hebron
            (32.5055, 35.2983),  # Jenin
            (31.8996, 35.2042),  # Bethlehem
        ]
        
        for i, driver in enumerate(drivers):
            # Use different base location for each driver
            base_lat, base_lng = base_locations[i % len(base_locations)]
            
            # Add small random offset
            offset_lat = random.uniform(-0.01, 0.01)
            offset_lng = random.uniform(-0.01, 0.01)
            
            initial_lat = base_lat + offset_lat
            initial_lng = base_lng + offset_lng
            
            # Update driver's position
            driver.current_latitude = Decimal(str(round(initial_lat, 6)))
            driver.current_longitude = Decimal(str(round(initial_lng, 6)))
            driver.last_location_update = timezone.now()
            driver.is_on_duty = True
            driver.save()
            
            # Create initial location record
            Location.objects.create(
                driver=driver,
                latitude=driver.current_latitude,
                longitude=driver.current_longitude
            )
            
            self.stdout.write(
                f'Initialized {driver.username} at {initial_lat:.6f}, {initial_lng:.6f}'
            )

    def generate_new_position(self, driver, radius):
        """Generate a new position within radius of current position"""
        if not driver.current_latitude or not driver.current_longitude:
            # If no current position, use a default location (Jerusalem)
            current_lat = 31.9046
            current_lng = 35.2066
        else:
            current_lat = float(driver.current_latitude)
            current_lng = float(driver.current_longitude)

        # Generate random angle and distance
        angle = random.uniform(0, 2 * math.pi)
        distance = random.uniform(0, radius)

        # Calculate new coordinates
        new_lat = current_lat + (distance * math.cos(angle))
        new_lng = current_lng + (distance * math.sin(angle))

        return round(new_lat, 6), round(new_lng, 6)

    def update_driver_location(self, driver, latitude, longitude):
        """Update driver's location in database"""
        driver.current_latitude = Decimal(str(latitude))
        driver.current_longitude = Decimal(str(longitude))
        driver.last_location_update = timezone.now()
        driver.save()

        # Create location history record
        Location.objects.create(
            driver=driver,
            latitude=driver.current_latitude,
            longitude=driver.current_longitude
        )

    def send_websocket_update(self, channel_layer, driver, latitude, longitude):
        """Send location update via WebSocket"""
        try:
            async_to_sync(channel_layer.group_send)(
                'public_location_updates',
                {
                    'type': 'location_update',
                    'driver_id': str(driver.id),
                    'username': driver.username,
                    'phone_number': driver.phone_number,
                    'village': str(driver.village) if driver.village else '',
                    'latitude': latitude,
                    'longitude': longitude,
                    'is_simulation': True
                }
            )
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'WebSocket update failed: {e}')
            )
