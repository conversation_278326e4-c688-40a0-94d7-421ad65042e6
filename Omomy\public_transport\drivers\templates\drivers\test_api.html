{% extends "base.html" %}

{% block title %}API Test{% endblock %}

{% block content %}
<div class="container">
    <h1>API Test</h1>
    <button onclick="testAPI()" class="btn btn-primary">Test API</button>
    <div id="result" class="mt-3"></div>
</div>

<script>
async function testAPI() {
    try {
        console.log('Testing API...');
        const response = await fetch('/drivers/api-drivers/?village=كفرعين');
        const data = await response.json();
        
        console.log('API Response:', data);
        console.log('Data type:', typeof data);
        console.log('Is array:', Array.isArray(data));
        console.log('Length:', data.length);
        
        let resultHTML = '<h3>API Response:</h3>';
        resultHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        
        if (data.length > 0) {
            const driver = data[0];
            console.log('First driver:', driver);
            console.log('Driver latitude:', driver.latitude, 'type:', typeof driver.latitude);
            console.log('Driver longitude:', driver.longitude, 'type:', typeof driver.longitude);
            console.log('Driver is_on_duty:', driver.is_on_duty, 'type:', typeof driver.is_on_duty);
            
            resultHTML += '<h3>Analysis:</h3>';
            resultHTML += '<ul>';
            resultHTML += '<li>Driver: ' + driver.username + '</li>';
            resultHTML += '<li>Latitude: ' + driver.latitude + ' (type: ' + typeof driver.latitude + ')</li>';
            resultHTML += '<li>Longitude: ' + driver.longitude + ' (type: ' + typeof driver.longitude + ')</li>';
            resultHTML += '<li>On Duty: ' + driver.is_on_duty + ' (type: ' + typeof driver.is_on_duty + ')</li>';
            resultHTML += '</ul>';
            
            // Test marker creation logic
            if (driver.latitude && driver.longitude && driver.is_on_duty) {
                resultHTML += '<div class="alert alert-success">✅ Driver should appear on map!</div>';
                console.log('✅ Driver should appear on map!');
            } else {
                resultHTML += '<div class="alert alert-danger">❌ Driver will NOT appear on map</div>';
                resultHTML += '<ul>';
                resultHTML += '<li>Has latitude: ' + !!driver.latitude + '</li>';
                resultHTML += '<li>Has longitude: ' + !!driver.longitude + '</li>';
                resultHTML += '<li>Is on duty: ' + !!driver.is_on_duty + '</li>';
                resultHTML += '</ul>';
                console.log('❌ Driver will NOT appear on map');
                console.log('  - Has latitude:', !!driver.latitude);
                console.log('  - Has longitude:', !!driver.longitude);
                console.log('  - Is on duty:', !!driver.is_on_duty);
            }
        }
        
        document.getElementById('result').innerHTML = resultHTML;
    } catch (error) {
        console.error('Error:', error);
        document.getElementById('result').innerHTML = '<div class="alert alert-danger">Error: ' + error.message + '</div>';
    }
}
</script>
{% endblock %}
