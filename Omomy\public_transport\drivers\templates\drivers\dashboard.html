{% extends "base.html" %}
{% load static %}

{% block title %}لوحة التحكم{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">مرحبًا, {{ driver.username }}</h4>
                <div class="mt-2">
                </div>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <strong>القرية:</strong> {{ driver.village }} |
                        <strong>رقم الهاتف:</strong> {{ driver.phone_number }}
                    </div>
                    <button id="toggleDutyBtn" class="btn {% if driver.is_on_duty %}btn-danger{% else %}btn-success{% endif %}">
                        {% if driver.is_on_duty %}
                            إيقاف الدوام
                        {% else %}
                            بدء الدوام
                        {% endif %}
                    </button>
                </div>

                <div class="alert alert-info" id="statusAlert">
                    {% if driver.is_on_duty %}
                        حالة الدوام: <strong class="text-success">جاري العمل</strong>
                        <p class="mb-0">سيتم تتبع موقعك تلقائيًا كل 10 ثوانِ</p>
                    {% else %}
                        حالة الدوام: <strong class="text-danger">متوقف</strong>
                        <p class="mb-0">لن يتم تتبع موقعك حتى تقوم بتشغيل الدوام</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">خريطة الموقع</h4>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5>المسارات المسموحة: {{ driver.permitted_routes }}</h5>
                </div>
                <div id="map" style="height: 500px; width: 100%;" data-permitted-routes="{{ driver.permitted_routes }}"></div>
                

                {% if selected_village %}
                <div class="mt-3">
                    <h5>سائقي قرية {{ selected_village }}</h5>
                    <ul id="drivers-list" class="list-group">
                        {% for driver in drivers %}
                        <li class="list-group-item">{{ driver.username }} - {{ driver.status }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register("{% static 'sw.js' %}")
    .then(registration => {
      console.log('ServiceWorker registration successful');
    })
    .catch(err => {
      console.error('ServiceWorker registration failed: ', err);
    });
}
</script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<style>
    #map {
        height: 500px;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
        z-index: 1;
    }
    .leaflet-control-attribution {
        font-size: 11px;
    }
    .driver-location {
        background-color: #007bff;
        border-radius: 50%;
        border: 2px solid white;
    }
</style>
<script>
    let map;
    let userMarker;
    let positionInterval;

    function checkPrerequisites() {
        // Check if Leaflet loaded
        if (typeof L === 'undefined') {
            console.error('LeafletJS not loaded - check network tab');
            alert('Failed to load map library. Please check console for details.');
            return false;
        }

        // Check if map container exists
        const mapEl = document.getElementById('map');
        if (!mapEl) {
            console.error('Map container element not found');
            return false;
        }
        
        // Verify container visibility and dimensions
        const style = window.getComputedStyle(mapEl);
        if (style.display === 'none') {
            console.error('Map container is hidden');
            mapEl.style.display = 'block';
        }
        
        mapEl.style.height = mapEl.style.height || '500px'; 
        mapEl.style.width = mapEl.style.width || '100%';
        return true;
    }

    function initializeMap() {
        console.log('Initializing map...');
        
        if (!checkPrerequisites()) {
            console.error('Missing prerequisites for map initialization');
            return;
        }

        try {
            console.log('Creating Leaflet map instance');
            if (typeof L === 'undefined') {
                throw new Error('Leaflet library not loaded - check network requests');
            }

            const mapEl = document.getElementById('map');
            const permittedRoutes = mapEl.dataset.permittedRoutes;
            
            // Only create map if it doesn't exist
            if (!map) {
                map = L.map('map', {
                    zoomControl: true,
                    preferCanvas: true
                }).setView([31.9, 35.2], 11);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    maxZoom: 18,
                    minZoom: 10
                }).addTo(map);

                L.control.scale({position: 'bottomleft'}).addTo(map);

                // Add permitted routes if available
                if (permittedRoutes) {
                    const routes = permittedRoutes.split(',').map(r => r.trim());
                    for (const route of routes) {
                        const [start, end] = route.split('-').map(loc => loc.trim());
                        // You should implement getRouteCoords or integrate with actual API
                        // Example placeholder:
                        const coords = [
                            [31.9, 35.2],
                            [32.0, 35.3]
                        ];
                        L.polyline(coords, { color: 'blue' }).addTo(map)
                            .bindPopup(`<b>${route}</b>`);
                    }
                }

                console.log('Map initialized successfully');

                {% if driver.is_on_duty %}
                    initializeLocationTracking();
                {% endif %}
            }
        } catch (error) {
            console.error('Error initializing map:', error);
            alert('Error initializing map: ' + error.message);
        }
    }

    let driverSocket;
    
    function initializeLocationTracking() {
        // Set up WebSocket connection
        const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
        driverSocket = new WebSocket(
            `${wsScheme}://${window.location.host}/ws/drivers/location/`
        );

        driverSocket.onopen = function(e) {
            console.log('WebSocket connection established');
        };

        driverSocket.onclose = function(e) {
            console.log('WebSocket disconnected, attempting to reconnect...');
            setTimeout(initializeLocationTracking, 5000);
        };

        driverSocket.onerror = function(err) {
            console.error('WebSocket error:', err);
        };

        if (positionInterval) {
            clearInterval(positionInterval);
        }
        
        positionInterval = setInterval(() => {
            navigator.geolocation.getCurrentPosition(pos => {
                const latitude = pos.coords.latitude;
                const longitude = pos.coords.longitude;

                // Update location on the map
                updateLocation(latitude, longitude);

                // Send location via WebSocket
                if (driverSocket.readyState === WebSocket.OPEN) {
                    driverSocket.send(JSON.stringify({
                        'latitude': latitude,
                        'longitude': longitude
                    }));
                } else {
                    console.error('WebSocket not ready, falling back to HTTP');
                    fetch('/drivers/update-location/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: `latitude=${latitude}&longitude=${longitude}`
                    }).catch(console.error);
                }
            }, 
            err => {
                console.error('Geolocation error:', err);
            },
            {
                enableHighAccuracy: true,
                maximumAge: 10000,
                timeout: 30000
            });
        }, 10000);
    }

    function updateLocation(position) {
        const latitude = position.coords.latitude;
        const longitude = position.coords.longitude;

        // Update map marker
        if (userMarker) {
            userMarker.setLatLng([latitude, longitude]);
        } else {
            userMarker = L.marker([latitude, longitude]).addTo(map)
                .bindPopup("موقعك الحالي").openPopup();
        }

        // Send location to server with driver's username
        fetch('/drivers/update-location/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': '{{ csrf_token }}',
            },
            body: `username={{ driver.username }}&latitude=${latitude}&longitude=${longitude}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.status !== 'success') {
                console.error('Location update failed:', data.message);
            }
        })
        .catch(error => {
            console.error('Location update error:', error);
        });
    }

    function startTracking() {
        if (!navigator.geolocation) {
            alert("المتصفح لا يدعم تحديد الموقع الجغرافي");
            return;
        }
        
        navigator.geolocation.getCurrentPosition(updateLocation, (error) => {
console.error("Geolocation error:", error.message);
    }
);

        positionInterval = setInterval(() => {
            navigator.geolocation.getCurrentPosition(updateLocation);
        }, 10000); // every 10 seconds
    }

    document.addEventListener("DOMContentLoaded", function () {
        startTracking();
    });

    function stopLocationTracking() {
        if (positionInterval) {
            clearInterval(positionInterval);
            positionInterval = null;
        }
        if (driverSocket) {
            driverSocket.close();
        }
        if (userMarker && map) {
            map.removeLayer(userMarker);
            userMarker = null;
        }
    }

    let locationSocket = null;

    function setupLocationWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/public/location/`;
        
        console.log('Connecting to location WebSocket:', wsUrl);
        locationSocket = new WebSocket(wsUrl);

        locationSocket.onopen = function(e) {
            console.log('Location WebSocket connected');
        };

        locationSocket.onclose = function(e) {
            console.log('Location WebSocket connection closed. Reconnecting...');
            setTimeout(setupLocationWebSocket, 5000);
        };

        locationSocket.onerror = function(e) {
            console.error('Location WebSocket error:', e);
        };
    }



    document.addEventListener('DOMContentLoaded', function() {
        initializeMap();
        setupLocationWebSocket();
        
        // Update status message to reflect real-time updates
        const statusAlert = document.getElementById('statusAlert');
        if (statusAlert && statusAlert.innerHTML.includes('10 ثوانِ')) {
            statusAlert.innerHTML = statusAlert.innerHTML.replace(
                'سيتم تتبع موقعك تلقائيًا كل 10 ثوانِ',
                'سيتم تحديث موقعك في الوقت الفعلي'
            );
        }
        
        document.getElementById('toggleDutyBtn').addEventListener('click', function() {
            const button = document.getElementById('toggleDutyBtn');
            const isStartingDuty = button.textContent.includes('بدء الدوام');
            
            if (isStartingDuty) {
                navigator.geolocation.getCurrentPosition(pos => {
                    const { latitude, longitude } = pos.coords;
                    fetch('/drivers/toggle-duty/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: `latitude=${latitude}&longitude=${longitude}`
                    })
                    .then(response => response.json())
                    .then(handleToggleResponse)
                    .catch(err => {
                        console.error('Error:', err);
                        alert('حدث خطأ أثناء محاولة بدء الدوام');
                    });
                }, err => {
                    console.error('Geolocation error:', err);
                    alert('يجب السماح بالوصول إلى الموقع الجغرافي لبدء الدوام');
                }, {
                    enableHighAccuracy: true,
                    timeout: 30000
                });
            } else {
                fetch('/drivers/toggle-duty/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': '{{ csrf_token }}'
                    }
                })
                .then(response => response.json())
                .then(handleToggleResponse)
                .catch(err => {
                    console.error('Error:', err);
                    alert('حدث خطأ أثناء محاولة إيقاف الدوام');
                });
            }
        });
    });

    function handleToggleResponse(data) {
        const statusAlert = document.getElementById('statusAlert');
        const button = document.getElementById('toggleDutyBtn');
        window.isOnDuty = data.is_on_duty;

        if (data.is_on_duty) {
            button.textContent = 'إيقاف الدوام';
            

            button.classList.remove('btn-success');
            button.classList.add('btn-danger');
            statusAlert.innerHTML = `حالة الدوام: <strong class="text-success">جاري العمل</strong>
                <p class="mb-0">سيتم تتبع موقعك تلقائيًا كل 10 ثوانِ</p>`;
            initializeLocationTracking();
        } else {
            button.textContent = 'بدء الدوام';
            button.classList.remove('btn-danger');
            button.classList.add('btn-success');
            statusAlert.innerHTML = `حالة الدوام: <strong class="text-danger">متوقف</strong>
                <p class="mb-0">لن يتم تتبع موقعك حتى تقوم بتشغيل الدوام</p>`;
            stopLocationTracking();
        }
    }
</script>
{% endblock %}
