#!/usr/bin/env python3
"""
PythonAnywhere Setup and Configuration Script
============================================

This script prepares your Django Channels project for deployment on PythonAnywhere.
It handles environment setup, dependency installation, and server configuration.

Usage:
    python setup_pythonanywhere.py

This script will:
1. Check and install required dependencies
2. Configure Django settings for PythonAnywhere
3. Run database migrations
4. Collect static files
5. Test the ASGI application
6. Provide instructions for running with Daphne
"""

import os
import sys
import subprocess
import django
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_step(step_num, description):
    """Print a formatted step"""
    print(f"\n[Step {step_num}] {description}")
    print("-" * 40)

def check_python_version():
    """Check Python version compatibility"""
    print_step(1, "Checking Python Version")
    
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required for Django Channels")
        return False
    
    print("✅ Python version is compatible")
    return True

def setup_environment():
    """Setup environment variables and paths"""
    print_step(2, "Setting up Environment")
    
    # Add current directory to Python path
    current_dir = Path(__file__).resolve().parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Set environment variables
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'public_transport.settings')
    os.environ.setdefault('DJANGO_ALLOW_ASYNC_UNSAFE', 'true')
    
    print(f"✅ Working directory: {current_dir}")
    print("✅ Environment variables set")

def install_dependencies():
    """Check and install required dependencies"""
    print_step(3, "Checking Dependencies")
    
    # Read requirements
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    print("📦 Installing dependencies from requirements.txt...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True, text=True)
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("Please install manually using: pip install -r requirements.txt")
        return False
    
    return True

def setup_django():
    """Setup Django and run initial configuration"""
    print_step(4, "Configuring Django")
    
    try:
        # Setup Django
        django.setup()
        print("✅ Django setup completed")
        
        # Test database connection
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def run_migrations():
    """Run database migrations"""
    print_step(5, "Running Database Migrations")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Run migrations
        execute_from_command_line(['manage.py', 'migrate', '--noinput'])
        print("✅ Database migrations completed")
        return True
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def collect_static_files():
    """Collect static files"""
    print_step(6, "Collecting Static Files")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Collect static files
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
        print("✅ Static files collected")
        return True
    except Exception as e:
        print(f"❌ Static file collection failed: {e}")
        return False

def test_asgi_application():
    """Test ASGI application import"""
    print_step(7, "Testing ASGI Application")
    
    try:
        from public_transport.asgi import application
        print("✅ ASGI application imported successfully")
        print(f"✅ Application type: {type(application)}")
        return True
    except Exception as e:
        print(f"❌ ASGI application test failed: {e}")
        return False

def create_startup_scripts():
    """Create convenient startup scripts"""
    print_step(8, "Creating Startup Scripts")
    
    # Create a simple start script
    start_script = """#!/bin/bash
# Simple startup script for PythonAnywhere
echo "Starting Django Channels project with Daphne..."
python pythonanywhere_daphne.py 8000
"""
    
    with open("start_server.sh", "w") as f:
        f.write(start_script)
    
    # Make it executable
    os.chmod("start_server.sh", 0o755)
    
    print("✅ Created start_server.sh")
    print("✅ Startup scripts ready")

def print_final_instructions():
    """Print final setup instructions"""
    print_header("Setup Complete!")
    
    print("""
🎉 Your Django Channels project is ready for PythonAnywhere!

To start your server, you have several options:

1. Using the main runner script:
   python run_daphne.py 8000

2. Using the simple launcher:
   python pythonanywhere_daphne.py 8000

3. Using Daphne directly:
   daphne -p 8000 -b 0.0.0.0 public_transport.asgi:application

4. Using the bash script:
   ./start_server.sh

📝 Important Notes:
- The server will run on port 8000 by default
- Use 0.0.0.0 as the bind address for PythonAnywhere
- Your project supports WebSockets through Django Channels
- Static files are served from the staticfiles directory
- Database is SQLite (suitable for development/testing)

🔧 For production on PythonAnywhere:
- Consider using Redis for channel layers instead of InMemory
- Update ALLOWED_HOSTS in settings.py with your domain
- Set DEBUG = False in production
- Configure proper logging

🚀 Your project is ready to run!
""")

def main():
    """Main setup function"""
    print_header("PythonAnywhere Setup Script")
    print("This script will prepare your Django Channels project for PythonAnywhere")
    
    # Run setup steps
    steps = [
        check_python_version,
        setup_environment,
        install_dependencies,
        setup_django,
        run_migrations,
        collect_static_files,
        test_asgi_application,
        create_startup_scripts
    ]
    
    for step_func in steps:
        if not step_func():
            print(f"\n❌ Setup failed at: {step_func.__name__}")
            print("Please fix the errors above and run the script again.")
            sys.exit(1)
    
    # Print final instructions
    print_final_instructions()

if __name__ == '__main__':
    main()
