<button onclick="testLocationUpdate()" class="btn btn-warning">Test Manual Update</button>

<script>
function testLocationUpdate() {
    // Increment current coordinates
    simulatedLat += 0.001;
    simulatedLng += 0.001;
    
    fetch('/drivers/update-location/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: `latitude=${simulatedLat}&longitude=${simulatedLng}`
    }).then(response => response.json())
    .then(data => {
        console.log('Manual update result:', data);
        alert(`Updated to: ${simulatedLat}, ${simulatedLng}`);
    });
}
</script>