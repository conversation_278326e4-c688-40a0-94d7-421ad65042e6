#!/usr/bin/env python3
"""
Simple Django Server Runner for PythonAnywhere
==============================================

This script runs your Django project using the standard Django development server
instead of Daphne, which is better for PythonAnywhere compatibility when WebSocket
functionality is disabled.

Usage:
    python run_simple_server.py [port]

Default port is 8000 if not specified.

This script:
- Uses Django's built-in runserver (more stable on PythonAnywhere)
- Handles environment setup
- Runs migrations and collects static files
- Provides better error handling for shared hosting environments
"""

import os
import sys
import subprocess
import django
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 50)
    print(f" {title}")
    print("=" * 50)

def setup_environment():
    """Setup environment variables and paths"""
    print("Setting up environment...")
    
    # Add current directory to Python path
    current_dir = Path(__file__).resolve().parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Set environment variables
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'public_transport.settings')
    
    print(f"✅ Working directory: {current_dir}")
    print("✅ Environment variables set")

def setup_django():
    """Setup Django and run initial configuration"""
    print("Setting up Django...")
    
    try:
        # Setup Django
        django.setup()
        print("✅ Django setup completed")
        
        # Test database connection
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def run_migrations():
    """Run database migrations"""
    print("Running database migrations...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Run migrations
        execute_from_command_line(['manage.py', 'migrate', '--noinput'])
        print("✅ Database migrations completed")
        return True
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def collect_static_files():
    """Collect static files"""
    print("Collecting static files...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Collect static files
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
        print("✅ Static files collected")
        return True
    except Exception as e:
        print(f"❌ Static file collection failed: {e}")
        return False

def start_django_server(port=8000):
    """Start Django development server"""
    print(f"Starting Django server on port {port}...")
    
    try:
        from django.core.management import execute_from_command_line
        
        print("=" * 50)
        print("SERVER STARTING - Press Ctrl+C to stop")
        print(f"Server will be available at: http://0.0.0.0:{port}")
        print("=" * 50)
        
        # Start Django development server
        execute_from_command_line([
            'manage.py', 
            'runserver', 
            f'0.0.0.0:{port}',
            '--noreload'  # Disable auto-reload for stability on PythonAnywhere
        ])
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print_header("Simple Django Server for PythonAnywhere")
    
    # Parse command line arguments
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"❌ Invalid port number: {sys.argv[1]}")
            sys.exit(1)
    
    print(f"Target port: {port}")
    
    # Setup steps
    setup_environment()
    
    if not setup_django():
        print("❌ Django setup failed. Please check the errors above.")
        sys.exit(1)
    
    if not run_migrations():
        print("❌ Migration failed. Please check the errors above.")
        sys.exit(1)
    
    if not collect_static_files():
        print("❌ Static file collection failed. Please check the errors above.")
        sys.exit(1)
    
    # Start server
    print("✅ All checks passed. Starting Django server...")
    success = start_django_server(port)
    
    if not success:
        print("❌ Failed to start Django server")
        sys.exit(1)

if __name__ == '__main__':
    main()
