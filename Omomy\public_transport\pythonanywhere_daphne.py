#!/usr/bin/env python3
"""
Simple PythonAnywhere Daphne Launcher
====================================

Minimal script to run Django Channels project with <PERSON> on PythonAnywhere.

Usage in PythonAnywhere console:
    python pythonanywhere_daphne.py

This will start the server on port 8000 by default.
You can also specify a different port:
    python pythonanywhere_daphne.py 8080
"""

import os
import sys
import django
from pathlib import Path

def setup_django():
    """Setup Django environment"""
    # Add current directory to Python path
    current_dir = Path(__file__).resolve().parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'public_transport.settings')
    os.environ.setdefault('DJANGO_ALLOW_ASYNC_UNSAFE', 'true')
    
    # Setup Django
    django.setup()
    print("✓ Django setup completed")

def run_daphne(port=8000):
    """Run Daphne server"""
    try:
        # Import Daphne components
        from daphne.cli import CommandLineInterface
        
        # Prepare arguments for Daphne
        sys.argv = [
            'daphne',
            '-p', str(port),
            '-b', '0.0.0.0',
            '--verbosity', '2',
            'public_transport.asgi:application'
        ]
        
        print(f"Starting Daphne server on port {port}...")
        print("Press Ctrl+C to stop the server")
        print("-" * 40)
        
        # Start Daphne
        cli = CommandLineInterface()
        cli.run()
        
    except KeyboardInterrupt:
        print("\nServer stopped")
    except Exception as e:
        print(f"Error starting server: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("PythonAnywhere Daphne Launcher")
    print("=" * 35)
    
    # Get port from command line
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"Invalid port: {sys.argv[1]}")
            sys.exit(1)
    
    # Setup Django
    setup_django()
    
    # Run server
    run_daphne(port)

if __name__ == '__main__':
    main()
