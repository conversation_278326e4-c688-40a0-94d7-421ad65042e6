from django.core.management.base import BaseCommand
from drivers.models import Driver

class Command(BaseCommand):
    help = 'Creates a test driver account for authentication testing'

    def handle(self, *args, **options):
        driver, created = Driver.objects.get_or_create(
            username='testdriver',
            defaults={
                'village': 'Test Village',
                'phone_number': '**********',
                'is_active': True
            }
        )
        if created:
            driver.set_password('test123')
            driver.save()
            self.stdout.write(self.style.SUCCESS('Successfully created test driver'))
        else:
            self.stdout.write('Test driver already exists')
