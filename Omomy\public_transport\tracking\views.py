from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate
from django.utils import timezone
from .models import Location
from drivers.models import Driver
import json
from django.core.cache import cache

@require_POST
@csrf_exempt
def update_location(request):
    try:
        data = json.loads(request.body)
        username = data.get('username')
        latitude = data.get('latitude')
        longitude = data.get('longitude')

        if not username or latitude is None or longitude is None:
            return JsonResponse({'status': 'error', 'message': 'Missing required fields'}, status=400)

        # Validate coordinates
        lat_val = float(latitude)
        lng_val = float(longitude)

        if not (-90 <= lat_val <= 90) or not (-180 <= lng_val <= 180):
            return JsonResponse({'status': 'error', 'message': 'Invalid coordinates'}, status=400)

        driver = Driver.objects.get(username=username)

        # Update driver's current location
        driver.current_latitude = lat_val
        driver.current_longitude = lng_val
        driver.last_location_update = timezone.now()
        driver.save()

        # Create location history record
        location = Location(
            driver=driver,
            latitude=lat_val,
            longitude=lng_val
        )
        location.save()

        return JsonResponse({'status': 'success'})

    except Driver.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'Driver not found'}, status=404)
    except (ValueError, TypeError):
        return JsonResponse({'status': 'error', 'message': 'Invalid coordinate format'}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
def get_live_locations(request):
    active_drivers = Driver.objects.filter(is_on_duty=True)
    locations = []
    
    for driver in active_drivers:
        last_location = Location.objects.filter(driver=driver).last()
        if last_location:
            locations.append({
                'driver': driver.username,
                'village': driver.village,
                'latitude': last_location.latitude,
                'longitude': last_location.longitude,
                'timestamp': last_location.timestamp.isoformat()
            })
    
    return JsonResponse({'locations': locations})

@csrf_exempt
def get_route_coordinates(request):
    try:
        data = json.loads(request.body)
        start = data.get('start')
        end = data.get('end')
        
        # TODO: Replace with actual coordinates from a mapping service API
        # For now returning mock coordinates between some known places
        if start and end:
            if 'رام الله' in start and 'كفرعين' in end:
                return JsonResponse({
                    'coordinates': [
                        [31.9042, 35.2034], # Ramallah
                        [31.9167, 35.2622], # Beituniya
                        [31.9520, 35.2726]  # Kafr Ein
                    ]
                })
        
        return JsonResponse({'error': 'Route not found'}, status=404)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)
